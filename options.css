* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  background-color: #f5f5f5;
  color: #333;
  line-height: 1.6;
}

.container {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

header {
  text-align: center;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid #e0e0e0;
}

h1 {
  font-size: 28px;
  color: #cc0000;
  margin-bottom: 8px;
}

.subtitle {
  font-size: 16px;
  color: #666;
}

.card {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 20px;
  margin-bottom: 24px;
}

h2 {
  font-size: 20px;
  margin-bottom: 16px;
  color: #333;
  border-bottom: 1px solid #eee;
  padding-bottom: 8px;
}

.option-group {
  margin-bottom: 24px;
}

h3 {
  font-size: 16px;
  margin-bottom: 12px;
  color: #555;
}

h4 {
  font-size: 14px;
  margin-bottom: 10px;
  color: #666;
  margin-left: 5px;
}

.option-row, .checkbox-row {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  padding: 8px 0;
}

.option-row label {
  flex: 0 0 180px;
  font-weight: 500;
}

.checkbox-row {
  display: flex;
  align-items: center;
}

.checkbox-row input[type="checkbox"] {
  margin-right: 10px;
  width: 18px;
  height: 18px;
}

input[type="text"], select {
  padding: 8px 12px;
  border-radius: 4px;
  border: 1px solid #ddd;
  font-size: 14px;
  flex: 1;
}

/* Ant Design style range container */
.range-container {
  flex: 1;
  margin-right: 10px;
  position: relative;
  margin-bottom: 30px;
  padding: 8px 0;
}

.range-container input[type="range"] {
  width: 100%;
  height: 4px;
  background: #f5f5f5;
  border-radius: 2px;
  outline: none;
  -webkit-appearance: none;
  appearance: none;
  position: relative;
  cursor: pointer;
}

/* Ant Design style track (active portion) */
.range-container input[type="range"]::-webkit-slider-runnable-track {
  width: 100%;
  height: 4px;
  background: #f5f5f5;
  border-radius: 2px;
}

.range-container input[type="range"]::-moz-range-track {
  width: 100%;
  height: 4px;
  background: #f5f5f5;
  border-radius: 2px;
  border: none;
}

/* Ant Design style thumb/handle */
.range-container input[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 14px;
  height: 14px;
  border-radius: 50%;
  background: #fff;
  cursor: pointer;
  border: 2px solid #1890ff;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
  position: relative;
  z-index: 3;
}

.range-container input[type="range"]::-webkit-slider-thumb:hover {
  border-color: #40a9ff;
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
  transform: scale(1.1);
}

.range-container input[type="range"]::-webkit-slider-thumb:active {
  border-color: #096dd9;
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.5);
  transform: scale(1.2);
}

/* Firefox thumb styling */
.range-container input[type="range"]::-moz-range-thumb {
  width: 14px;
  height: 14px;
  border-radius: 50%;
  background: #fff;
  cursor: pointer;
  border: 2px solid #1890ff;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.range-container input[type="range"]::-moz-range-thumb:hover {
  border-color: #40a9ff;
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
  transform: scale(1.1);
}

/* Active track styling using pseudo-element */
.range-container {
  --active-width: 20%; /* Default to 960px position (20% of 720-1920 range) */
}

.range-container::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  height: 4px;
  background: #1890ff;
  border-radius: 2px;
  transform: translateY(-50%);
  width: var(--active-width);
  transition: width 0.2s ease;
  z-index: 1;
}

/* Ant Design style tick marks */
.range-container::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 4px;
  background:
    radial-gradient(circle at 0% 50%, #d9d9d9 2px, transparent 2px),
    radial-gradient(circle at 20% 50%, #d9d9d9 2px, transparent 2px),
    radial-gradient(circle at 40% 50%, #d9d9d9 2px, transparent 2px),
    radial-gradient(circle at 60% 50%, #d9d9d9 2px, transparent 2px),
    radial-gradient(circle at 80% 50%, #d9d9d9 2px, transparent 2px),
    radial-gradient(circle at 100% 50%, #d9d9d9 2px, transparent 2px);
  transform: translateY(-50%);
  pointer-events: none;
  z-index: 2;
}

/* Ant Design style range labels */
.range-labels {
  display: flex;
  justify-content: space-between;
  position: absolute;
  top: 22px;
  left: 0;
  right: 0;
  font-size: 12px;
  color: #8c8c8c;
  pointer-events: none;
}

.range-labels span {
  transform: translateX(-50%);
  white-space: nowrap;
  font-weight: 400;
  line-height: 1;
}

/* Adjust first and last labels to prevent overflow */
.range-labels span:first-child {
  transform: translateX(0);
}

.range-labels span:last-child {
  transform: translateX(-100%);
}

/* Focus styling */
.range-container input[type="range"]:focus {
  outline: none;
}

.range-container input[type="range"]:focus::-webkit-slider-thumb {
  border-color: #40a9ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* Keep original styling for range inputs without tick marks */
input[type="range"]:not(.range-container input[type="range"]) {
  flex: 1;
  margin-right: 10px;
}

button {
  padding: 8px 16px;
  border-radius: 4px;
  border: none;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.2s;
}

button:hover {
  opacity: 0.9;
}

#change-shortcut-btn {
  margin-left: 10px;
  background-color: #f0f0f0;
  border: 1px solid #ddd;
}

.button-row {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.button-row button {
  flex: 1;
  min-width: 120px;
  background-color: #f0f0f0;
  border: 1px solid #ddd;
}

.danger {
  background-color: #ffebee !important;
  color: #d32f2f !important;
  border: 1px solid #ffcdd2 !important;
}

footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #e0e0e0;
}

.primary {
  background-color: #cc0000;
  color: white;
  padding: 10px 20px;
  font-weight: 500;
}

.version {
  font-size: 12px;
  color: #999;
}
