* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  background-color: #f5f5f5;
  color: #333;
  line-height: 1.6;
}

.container {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

header {
  text-align: center;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid #e0e0e0;
}

h1 {
  font-size: 28px;
  color: #cc0000;
  margin-bottom: 8px;
}

.subtitle {
  font-size: 16px;
  color: #666;
}

.card {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 20px;
  margin-bottom: 24px;
}

h2 {
  font-size: 20px;
  margin-bottom: 16px;
  color: #333;
  border-bottom: 1px solid #eee;
  padding-bottom: 8px;
}

.option-group {
  margin-bottom: 24px;
}

h3 {
  font-size: 16px;
  margin-bottom: 12px;
  color: #555;
}

h4 {
  font-size: 14px;
  margin-bottom: 10px;
  color: #666;
  margin-left: 5px;
}

.option-row, .checkbox-row {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  padding: 8px 0;
}

.option-row label {
  flex: 0 0 180px;
  font-weight: 500;
}

.checkbox-row {
  display: flex;
  align-items: center;
}

.checkbox-row input[type="checkbox"] {
  margin-right: 10px;
  width: 18px;
  height: 18px;
}

input[type="text"], select {
  padding: 8px 12px;
  border-radius: 4px;
  border: 1px solid #ddd;
  font-size: 14px;
  flex: 1;
}

/* Custom Ant Design-style slider */
.ant-slider-container {
  flex: 1;
  margin-right: 10px;
  position: relative;
  padding: 12px 0 30px 0;
}

.ant-slider {
  position: relative;
  height: 12px;
  margin: 0;
  padding: 4px 0;
  cursor: pointer;
}

.ant-slider-rail {
  position: absolute;
  width: 100%;
  height: 4px;
  background-color: #f5f5f5;
  border-radius: 2px;
  top: 4px;
}

.ant-slider-track {
  position: absolute;
  height: 4px;
  background-color: #1890ff;
  border-radius: 2px;
  top: 4px;
  left: 0;
  transition: width 0.2s ease;
}

.ant-slider-step {
  position: absolute;
  width: 100%;
  height: 4px;
  top: 4px;
}

.ant-slider-dot {
  position: absolute;
  top: -2px;
  width: 8px;
  height: 8px;
  border: 2px solid #f5f5f5;
  background-color: #fff;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.2s ease;
  transform: translateX(-50%);
}

.ant-slider-dot-active {
  border-color: #1890ff;
}

.ant-slider-dot:hover {
  border-color: #40a9ff;
  transform: translateX(-50%) scale(1.2);
}

.ant-slider-handle {
  position: absolute;
  top: 0;
  width: 14px;
  height: 14px;
  background-color: #fff;
  border: 2px solid #1890ff;
  border-radius: 50%;
  cursor: grab;
  transition: all 0.2s ease;
  transform: translateX(-50%);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.ant-slider-handle:hover {
  border-color: #40a9ff;
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
  transform: translateX(-50%) scale(1.1);
}

.ant-slider-handle:active {
  border-color: #096dd9;
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.5);
  cursor: grabbing;
  transform: translateX(-50%) scale(1.2);
}

.ant-slider-mark {
  position: absolute;
  top: 18px;
  width: 100%;
  font-size: 12px;
}

.ant-slider-mark-text {
  position: absolute;
  display: inline-block;
  color: #8c8c8c;
  text-align: center;
  cursor: pointer;
  transform: translateX(-50%);
  white-space: nowrap;
  user-select: none;
}

.ant-slider-mark-text:hover {
  color: #1890ff;
}

/* Hide the native input but keep it functional */
.ant-slider-container input[type="range"] {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  cursor: pointer;
  z-index: 10;
}

/* Keep original styling for other range inputs */
input[type="range"]:not(.ant-slider-container input[type="range"]) {
  flex: 1;
  margin-right: 10px;
}

button {
  padding: 8px 16px;
  border-radius: 4px;
  border: none;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.2s;
}

button:hover {
  opacity: 0.9;
}

#change-shortcut-btn {
  margin-left: 10px;
  background-color: #f0f0f0;
  border: 1px solid #ddd;
}

.button-row {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.button-row button {
  flex: 1;
  min-width: 120px;
  background-color: #f0f0f0;
  border: 1px solid #ddd;
}

.danger {
  background-color: #ffebee !important;
  color: #d32f2f !important;
  border: 1px solid #ffcdd2 !important;
}

footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #e0e0e0;
}

.primary {
  background-color: #cc0000;
  color: white;
  padding: 10px 20px;
  font-weight: 500;
}

.version {
  font-size: 12px;
  color: #999;
}
